import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Post } from '../types';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';
import HeartIcon from './icons/HeartIcon';
import ChatBubbleIcon from './icons/ChatBubbleIcon';
import ShareIcon from './icons/ShareIcon';
import TrashIcon from './icons/TrashIcon';
import PencilIcon from './icons/PencilIcon';
import CommentThread from './CommentThread';
import ReplyInput from './ReplyInput';
import { getVideoInfo } from '../utils/videoUtils';

interface PostCardProps {
  post: Post;
  isCompact?: boolean;
}

const PostCard: React.FC<PostCardProps> = ({ post, isCompact = false }) => {
  const { currentUser, isAdmin } = useAuth();
  const { deletePost, toggleLikePost, addCommentToPost, deleteComment, toggleLikeComment } = usePosts();
  const navigate = useNavigate();
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [isExpanded, setIsExpanded] = useState(!isCompact);
  const [replyingTo, setReplyingTo] = useState<{ parentId: string; username: string } | null>(null);

  // Determine display name and avatar for the post
  const displayUsername = post.isAnonymous && !isAdmin ? 'Anonymous' : post.username;
  const displayAvatarUrl = post.isAnonymous && !isAdmin ? 'https://picsum.photos/seed/anonymous/100/100' : post.userAvatarUrl;

  const timeSince = (dateString: string): string => {
    const date = new Date(dateString);
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
    let interval = seconds / 31536000;
    if (interval > 1) return Math.floor(interval) + "y";
    interval = seconds / 2592000;
    if (interval > 1) return Math.floor(interval) + "mo";
    interval = seconds / 86400;
    if (interval > 1) return Math.floor(interval) + "d";
    interval = seconds / 3600;
    if (interval > 1) return Math.floor(interval) + "h";
    interval = seconds / 60;
    if (interval > 1) return Math.floor(interval) + "m";
    return Math.floor(seconds) + "s";
  };

  const handleCommentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim() && currentUser) {
      try {
        await addCommentToPost(post.id, newComment.trim(), currentUser);
        setNewComment('');
        // Keep comments open and card expanded after adding comment
        setShowComments(true);
        if (isCompact) {
          setIsExpanded(true);
        }
      } catch (error) {
        console.error('Error submitting comment:', error);
        alert('Failed to add comment. Please try again.');
      }
    }
  };

  const handleReply = (parentId: string, replyToUsername: string) => {
    setReplyingTo({ parentId, username: replyToUsername });
    setShowComments(true);
    if (isCompact) {
      setIsExpanded(true);
    }
  };

  const handleReplySubmit = async (replyText: string) => {
    if (replyingTo && currentUser) {
      await addCommentToPost(post.id, replyText, currentUser, replyingTo.parentId, replyingTo.username);
    }
  };

  const handleReplyCancel = () => {
    setReplyingTo(null);
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      await deleteComment(post.id, commentId);
    } catch (error) {
      console.error('Error deleting comment:', error);
      throw error;
    }
  };

  const handleToggleLikeComment = async (commentId: string) => {
    if (currentUser) {
      try {
        await toggleLikeComment(post.id, commentId, currentUser.id);
      } catch (error) {
        console.error('Error toggling comment like:', error);
        throw error;
      }
    }
  };

  const canEditOrDelete = isAdmin || currentUser?.id === post.userId || (post.isAnonymous && currentUser?.id === post.actualUserId);

  const handleCardClick = () => {
    if (isCompact) {
      setIsExpanded(!isExpanded);
    }
  };

  const handleUserClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Only navigate if it's not an anonymous post, or if admin is viewing anonymous post
    if (!post.isAnonymous || isAdmin) {
      const userId = post.isAnonymous ? post.actualUserId : post.userId;
      if (userId) {
        navigate(`/user/${userId}`);
      }
    }
  };

  return (
    <article
      className={`bg-neutral-surface rounded-lg shadow-xl overflow-hidden border border-neutral-border animate-slide-up hover-glow transition-all duration-300 cyber-border ${
        isCompact ? 'my-2 cursor-pointer hover:scale-[1.01]' : 'my-4 hover:scale-[1.02]'
      }`}
      onClick={handleCardClick}
    >
      <header className={`${isCompact ? 'p-3' : 'p-4'} flex items-center justify-between`}>
        <div className="flex items-center flex-1">
          <button
            className={`${isCompact ? 'w-8 h-8' : 'w-10 h-10'} rounded-full mr-3 border-2 border-brand-primary hover-scale transition-transform duration-200 overflow-hidden ${
              (!post.isAnonymous || isAdmin) ? 'cursor-pointer hover:border-brand-secondary' : 'cursor-default'
            }`}
            onClick={handleUserClick}
            disabled={post.isAnonymous && !isAdmin}
            title={(!post.isAnonymous || isAdmin) ? `View ${displayUsername}'s profile` : 'Anonymous user'}
          >
            <img
              src={displayAvatarUrl}
              alt={displayUsername}
              className="w-full h-full object-cover"
            />
          </button>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              {/* Username - clickable for non-anonymous posts or admin viewing anonymous */}
              {!post.isAnonymous || isAdmin ? (
                <button
                  className={`font-semibold text-neutral-100 hover:text-brand-primary transition-colors duration-200 ${isCompact ? 'text-sm' : ''}`}
                  onClick={handleUserClick}
                  title={`View ${displayUsername}'s profile`}
                >
                  {displayUsername}
                </button>
              ) : (
                <p className={`font-semibold text-neutral-100 ${isCompact ? 'text-sm' : ''}`}>{displayUsername}</p>
              )}

              {post.isAnonymous && isAdmin && (
                <span className="text-xs bg-accent-warning/20 text-yellow-400 px-2 py-0.5 rounded-full">
                  Actually: {post.username}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <p className="text-xs text-neutral-muted">{timeSince(post.timestamp)} ago</p>
              {isCompact && !isExpanded && (
                <span className="text-xs text-brand-primary animate-pulse">Click to expand</span>
              )}
            </div>
          </div>
        </div>
        {canEditOrDelete && (
          <div className="flex items-center space-x-1" onClick={(e) => e.stopPropagation()}>
            <Link
              to={`/edit-post/${post.id}`}
              className="text-neutral-muted hover:text-brand-primary p-1 rounded-md transition-all duration-200 hover-scale hover-text-glow"
              title="Edit Post"
            >
              <PencilIcon className={`${isCompact ? 'w-4 h-4' : 'w-5 h-5'}`} />
            </Link>
            <button
              onClick={(e) => {
                e.stopPropagation();
                deletePost(post.id);
              }}
              className="text-accent-error hover:text-red-400 p-1 rounded-md transition-all duration-200 hover-scale animate-cyber-flicker"
              title="Delete Post"
            >
              <TrashIcon className={`${isCompact ? 'w-4 h-4' : 'w-5 h-5'}`} />
            </button>
          </div>
        )}
      </header>

      {/* Show content based on compact mode and expansion state */}
      {isCompact && !isExpanded ? (
        // Compact view - show image/video (if exists) and title/caption
        <>
          {post.videoUrl ? (
            // Video post - show thumbnail with play overlay
            <div className="compact-image-hover relative">
              <img
                src={post.imageUrl}
                alt={`Video post by ${post.username}: ${post.caption}`}
                className="w-full h-48 object-cover transition-all duration-200 hover:brightness-110"
                onError={(e) => e.currentTarget.style.display='none'}
              />
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                <div className="w-16 h-16 bg-brand-primary bg-opacity-80 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200">
                  <svg className="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
          ) : post.imageUrl ? (
            // Regular image post
            <div className="compact-image-hover">
              <img
                src={post.imageUrl}
                alt={`Post by ${post.username}: ${post.caption}`}
                className="w-full h-48 object-cover transition-all duration-200 hover:brightness-110"
                onError={(e) => e.currentTarget.style.display='none'}
              />
            </div>
          ) : (post.caption || post.contentBody) ? (
            // Text-only post - display text in image area (Facebook style)
            <div className="w-full h-48 text-post-display border-t border-b border-neutral-border flex items-center justify-center p-4 relative overflow-hidden">
              <div className="relative z-10 text-center">
                <p className="text-neutral-100 text-lg font-medium leading-relaxed line-clamp-4 hover-text-glow transition-all duration-300">
                  {post.caption || post.contentBody}
                </p>
              </div>
            </div>
          ) : null}
          <div className={`${isCompact ? ((post.imageUrl || post.videoUrl || post.caption || post.contentBody) ? 'px-3 pb-2 pt-3' : 'px-3 pb-2 pt-1') : 'p-4'}`}>
            {/* Only show text below if there's media above, otherwise text is shown in the image area */}
            {(post.imageUrl || post.videoUrl) && post.caption && (
              <p className="text-neutral-200 text-sm font-medium line-clamp-2 whitespace-pre-wrap">{post.caption}</p>
            )}
            {(post.imageUrl || post.videoUrl) && !post.caption && post.contentBody && (
              <p className="text-neutral-100 text-sm line-clamp-2 whitespace-pre-wrap">{post.contentBody}</p>
            )}
          </div>
        </>
      ) : (
        // Full view - show everything
        <>
          {post.videoUrl ? (
            // Video post - show embedded video
            <div className="relative">
              {(() => {
                const videoInfo = getVideoInfo(post.videoUrl);
                if (videoInfo) {
                  return (
                    <div className="relative w-full" style={{ paddingBottom: '56.25%' /* 16:9 aspect ratio */ }}>
                      <iframe
                        src={videoInfo.embedUrl}
                        title={`Video: ${post.caption || 'Video post'}`}
                        className="absolute top-0 left-0 w-full h-full rounded-md"
                        frameBorder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      />
                    </div>
                  );
                } else {
                  // Fallback for invalid video URLs
                  return (
                    <div className="bg-neutral-base border border-neutral-border rounded-md p-4 text-center">
                      <p className="text-neutral-300 text-sm">Video unavailable</p>
                      <a
                        href={post.videoUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-brand-primary hover:underline text-xs"
                      >
                        View original link
                      </a>
                    </div>
                  );
                }
              })()}
            </div>
          ) : post.imageUrl ? (
            // Regular image post
            <img
              src={post.imageUrl}
              alt={`Post by ${post.username}: ${post.caption}`}
              className="w-full h-auto object-cover max-h-[70vh]"
              onError={(e) => e.currentTarget.style.display='none'}
            />
          ) : (post.caption || post.contentBody) ? (
            // Text-only post - display text in image area (Facebook style) - Full view
            <div className="w-full min-h-[200px] text-post-display border-t border-b border-neutral-border flex items-center justify-center p-6 relative overflow-hidden">
              <div className="relative z-10 text-center max-w-2xl">
                <p className="text-neutral-100 text-xl font-medium leading-relaxed hover-text-glow transition-all duration-300 whitespace-pre-wrap">
                  {post.caption || post.contentBody}
                </p>
              </div>
            </div>
          ) : null}

          <div className={`${isCompact ? 'p-3' : 'p-4'}`}>
            {/* Only show text below if there's media above, otherwise text is shown in the image area */}
            {(post.imageUrl || post.videoUrl) && post.caption && (
                <p className="text-neutral-200 text-sm italic mb-2 whitespace-pre-wrap">{post.caption}</p>
            )}
            {(post.imageUrl || post.videoUrl) && post.contentBody && (
               <p className="text-neutral-100 my-3 whitespace-pre-wrap">{post.contentBody}</p>
            )}

            {post.tags && post.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {post.tags.map(tag => (
                  <span key={tag} className="text-xs bg-neutral-base text-brand-primary px-2 py-0.5 rounded-full">
                    #{tag}
                  </span>
                ))}
              </div>
            )}

            <div className="flex items-center space-x-4 text-neutral-muted mb-3 pt-3 border-t border-neutral-border" onClick={(e) => e.stopPropagation()}>
              <button
                onClick={async (e) => {
                  e.stopPropagation();
                  if (currentUser) {
                    try {
                      await toggleLikePost(post.id, currentUser.id);
                    } catch (error) {
                      console.error('Error toggling like:', error);
                    }
                  }
                }}
                className={`flex items-center space-x-1 hover:text-red-500 transition-all duration-200 hover-scale hover-text-glow ${post.isLikedByCurrentUser ? 'text-red-500 animate-pulse-glow' : ''}`}
                title={post.isLikedByCurrentUser ? "Unlike" : "Like"}
                disabled={!currentUser}
              >
                <HeartIcon className={`${isCompact ? 'w-5 h-5' : 'w-6 h-6'}`} isFilled={post.isLikedByCurrentUser} /> <span>{post.likes}</span>
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowComments(!showComments);
                }}
                className="flex items-center space-x-1 hover:text-brand-primary transition-all duration-200 hover-scale hover-text-glow"
                title="Comments"
              >
                <ChatBubbleIcon className={`${isCompact ? 'w-5 h-5' : 'w-6 h-6'}`} /> <span>{post.comments.length}</span>
              </button>
              <button
                onClick={(e) => e.stopPropagation()}
                className="flex items-center space-x-1 hover:text-brand-primary transition-all duration-200 hover-scale hover-text-glow"
                title="Share (Placeholder)"
              >
                <ShareIcon className={`${isCompact ? 'w-5 h-5' : 'w-6 h-6'}`} />
              </button>
            </div>

            {showComments && (
              <div className="mt-4 space-y-3 pt-3 border-t border-neutral-border" onClick={(e) => e.stopPropagation()}>
                {post.comments.length === 0 && <p className="text-sm text-neutral-muted">No comments yet.</p>}

                {/* Threaded Comments */}
                {post.comments.map(comment => (
                  <CommentThread
                    key={comment.id}
                    comment={comment}
                    postId={post.id}
                    currentUser={currentUser}
                    isAdmin={isAdmin}
                    onReply={handleReply}
                    onDelete={handleDeleteComment}
                    onToggleLike={handleToggleLikeComment}
                  />
                ))}

                {/* Reply Input */}
                {replyingTo && currentUser && (
                  <ReplyInput
                    currentUser={currentUser}
                    onSubmit={handleReplySubmit}
                    onCancel={handleReplyCancel}
                    replyToUsername={replyingTo.username}
                  />
                )}

                {/* Main Comment Input */}
                {currentUser && !replyingTo && (
                  <form onSubmit={handleCommentSubmit} className="flex items-center space-x-2 mt-3">
                    <img src={currentUser.avatarUrl} alt={currentUser.username} className="w-8 h-8 rounded-full" />
                    <input
                      type="text"
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder="Add a comment..."
                      className="flex-grow bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-1 focus:ring-brand-primary focus:border-brand-primary text-sm"
                    />
                    <button type="submit" className="bg-brand-primary hover:bg-brand-secondary text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">Post</button>
                  </form>
                )}
                {!currentUser && <p className="text-sm text-neutral-muted mt-2">Please log in to comment.</p>}
              </div>
            )}
          </div>
        </>
      )}

    </article>
  );
};

export default PostCard;
// Script to create admin user if it doesn't exist
import { initializeApp } from 'firebase/app';
import { getAuth, createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { getFirestore, doc, setDoc, getDoc } from 'firebase/firestore';

// Firebase config (replace with your actual config)
const firebaseConfig = {
  apiKey: "AIzaSyBKqJqXqJqXqJqXqJqXqJqXqJqXqJqXqJq",
  authDomain: "s3kt0r-30ede.firebaseapp.com",
  projectId: "s3kt0r-30ede",
  storageBucket: "s3kt0r-30ede.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdefghijklmnop"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

const createAdminUser = async () => {
  const adminEmail = '<EMAIL>';
  const adminPassword = 'Tr1p$t0p3301';
  const adminUsername = 'admin_s3kt0r';

  try {
    // Check if admin user already exists
    const adminDoc = await getDoc(doc(db, 'users', 'admin_s3kt0r_truth'));
    if (adminDoc.exists()) {
      console.log('Admin user already exists');
      return;
    }

    // Create admin user
    const userCredential = await createUserWithEmailAndPassword(auth, adminEmail, adminPassword);
    const firebaseUser = userCredential.user;

    // Update display name
    await updateProfile(firebaseUser, { displayName: adminUsername });

    // Create admin user document
    const userData = {
      id: firebaseUser.uid,
      username: adminUsername,
      avatarUrl: `https://picsum.photos/seed/${adminUsername.replace(/\s+/g, '')}/100/100`,
      isActive: true,
      isPendingApproval: false,
      isAdmin: true,
      bio: 'Platform Administrator\nGuardian of the digital realm\nMaintaining order in chaos\n⚠️ With great power...'
    };

    await setDoc(doc(db, 'users', firebaseUser.uid), userData);
    console.log('Admin user created successfully');
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
};

createAdminUser();

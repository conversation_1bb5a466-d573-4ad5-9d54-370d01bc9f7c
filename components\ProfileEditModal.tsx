import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { User } from '../types';
import { uploadImage } from '../services/firebaseService';

interface ProfileEditModalProps {
  user: User;
  onClose: () => void;
  isAdmin?: boolean;
}

const ProfileEditModal: React.FC<ProfileEditModalProps> = ({ user, onClose, isAdmin = false }) => {
  const { updateUserProfile } = useAuth();
  const [bio, setBio] = useState(user.bio || '');
  const [avatarUrl, setAvatarUrl] = useState(user.avatarUrl);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        const dataUrl = reader.result as string;
        setAvatarPreview(dataUrl);
      };
      reader.readAsDataURL(file);
    } else {
      setAvatarFile(null);
      setAvatarPreview(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (bio.split('\n').length > 5) {
      alert('Bio cannot exceed 5 lines.');
      return;
    }

    setIsSubmitting(true);
    try {
      let finalAvatarUrl = avatarUrl;

      // If a new avatar file was selected, upload it to Firebase Storage
      if (avatarFile) {
        const timestamp = Date.now();
        const fileName = `avatars/${user.id}/${timestamp}_${avatarFile.name}`;
        finalAvatarUrl = await uploadImage(avatarFile, fileName);
      }

      updateUserProfile(user.id, {
        bio: bio.trim(),
        avatarUrl: finalAvatarUrl
      });
      alert('Profile updated successfully!');
      onClose();
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-scale-in">
      <div className="bg-neutral-surface rounded-lg shadow-2xl border border-neutral-border max-w-md w-full max-h-[90vh] overflow-y-auto animate-scale-in hover-glow cyber-border">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-brand-primary animate-text-glow hover-text-glow">
              {isAdmin ? `Edit ${user.username}'s Profile` : 'Edit Profile'}
            </h2>
            <button
              onClick={onClose}
              className="text-neutral-muted hover:text-neutral-100 transition-all duration-200 hover-scale hover-text-glow"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Avatar Section */}
            <div>
              <label className="block text-sm font-medium text-neutral-300 mb-3">Profile Picture</label>
              <div className="flex items-center space-x-4">
                <img
                  src={avatarPreview || avatarUrl}
                  alt={user.username}
                  className="w-20 h-20 rounded-full border-2 border-brand-primary hover-scale transition-transform duration-200"
                />
                <div className="flex-1">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="w-full text-sm text-neutral-300 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-brand-primary file:text-white hover:file:bg-brand-secondary cursor-pointer transition-all duration-200"
                  />
                  <p className="mt-1 text-xs text-neutral-muted">
                    Upload a new profile picture or keep the current one
                  </p>
                </div>
              </div>
            </div>

            {/* Bio Section */}
            <div>
              <label htmlFor="bio" className="block text-sm font-medium text-neutral-300 mb-1">
                Bio (up to 5 lines)
              </label>
              <textarea
                id="bio"
                value={bio}
                onChange={(e) => setBio(e.target.value)}
                placeholder="Tell us about yourself..."
                rows={5}
                maxLength={500}
                className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200 hover:border-brand-primary/50 hover-glow resize-none"
              />
              <div className="flex justify-between mt-1">
                <p className="text-xs text-neutral-muted">
                  {bio.split('\n').length}/5 lines
                </p>
                <p className="text-xs text-neutral-muted">
                  {bio.length}/500 characters
                </p>
              </div>
            </div>

            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 bg-neutral-base hover:bg-neutral-border text-neutral-100 font-semibold py-2.5 px-4 rounded-md transition-all duration-200 hover-scale hover-glow"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2.5 px-4 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-surface focus:ring-brand-secondary transform hover:scale-105 hover:shadow-lg hover:shadow-brand-primary/25 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none hover-glow"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ProfileEditModal;

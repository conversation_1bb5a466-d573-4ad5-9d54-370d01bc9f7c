import React, { createContext, useState, ReactNode, useCallback, useMemo, useEffect, useContext } from 'react';
import { Post, Comment, User } from '../types';
import { AuthContext } from './AuthContext';
import {
  getAllPosts,
  createPost as firebaseCreatePost,
  updatePost as firebaseUpdatePost,
  deletePost as firebaseDeletePost,
  getPostById,
  likePost,
  unlikePost,
  addCommentToPost as firebaseAddCommentToPost,
  deleteCommentFromPost as firebaseDeleteCommentFromPost,
  likeComment,
  unlikeComment
} from '../services/firebaseService';


type EditablePostData = Partial<Pick<Post, 'imageUrl' | 'videoUrl' | 'caption' | 'contentBody' | 'tags'>>;

interface PostsContextType {
  posts: Post[];
  loading: boolean;
  getPostById: (postId: string) => Post | undefined;
  addPost: (postData: Omit<Post, 'id' | 'likes' | 'comments' | 'timestamp' | 'isLikedByCurrentUser'>) => Promise<void>;
  editPost: (postId: string, updatedData: EditablePostData) => Promise<void>;
  deletePost: (postId: string) => Promise<void>;
  toggleLikePost: (postId: string, userId: string) => Promise<void>;
  addCommentToPost: (postId: string, commentText: string, currentUser: User, parentId?: string, replyToUsername?: string) => Promise<void>;
  deleteComment: (postId: string, commentId: string) => Promise<void>;
  toggleLikeComment: (postId: string, commentId: string, userId: string) => Promise<void>;
  refreshPosts: () => Promise<void>;
  updatePostsWithCurrentUser: (currentUserId: string | null) => void;
}

export const PostsContext = createContext<PostsContextType | undefined>(undefined);

interface PostsProviderProps {
  children: ReactNode;
}

export const PostsProvider: React.FC<PostsProviderProps> = ({ children }) => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const authContext = useContext(AuthContext);

  // Get current user from auth context
  const currentUser = authContext?.currentUser;
  const authLoading = authContext?.loading;

  const refreshPosts = useCallback(async () => {
    try {
      setLoading(true);

      // Check if user is authenticated before trying to load posts
      if (!currentUser) {
        console.log('PostsContext: No current user, clearing posts');
        setPosts([]);
        return;
      }

      console.log('PostsContext: Loading posts for user:', currentUser.username);
      const fetchedPosts = await getAllPosts();
      console.log('PostsContext: Fetched', fetchedPosts.length, 'posts');
      // Note: isLikedByCurrentUser will be set by PostsUpdater component
      setPosts(fetchedPosts);
    } catch (error) {
      console.error('Error loading posts:', error);
      // If there's a permission error, clear posts
      setPosts([]);
    } finally {
      setLoading(false);
    }
  }, [currentUser]);

  // Load posts when user authentication state changes
  useEffect(() => {
    console.log('PostsContext useEffect triggered:', {
      currentUser: currentUser?.username || 'none',
      authLoading,
      hasAuthContext: !!authContext
    });

    // Don't load posts if auth is still loading
    if (authLoading) {
      console.log('PostsContext: Auth still loading, skipping posts fetch');
      return;
    }

    refreshPosts();
  }, [currentUser, authLoading, refreshPosts]);

  const getPostById = useCallback((postId: string) => {
    return posts.find(post => post.id === postId);
  }, [posts]);

  const addPost = useCallback(async (postData: Omit<Post, 'id' | 'likes' | 'comments' | 'timestamp' | 'isLikedByCurrentUser'>) => {
    try {
      await firebaseCreatePost(postData);
      await refreshPosts(); // Refresh to get the new post
    } catch (error) {
      console.error('Error creating post:', error);
      throw error;
    }
  }, [refreshPosts]);

  const editPost = useCallback(async (postId: string, updatedData: EditablePostData) => {
    try {
      await firebaseUpdatePost(postId, updatedData);
      await refreshPosts(); // Refresh to get the updated post
    } catch (error) {
      console.error('Error updating post:', error);
      throw error;
    }
  }, [refreshPosts]);

  const deletePost = useCallback(async (postId: string) => {
    try {
      await firebaseDeletePost(postId);
      setPosts(prevPosts => prevPosts.filter(post => post.id !== postId));
    } catch (error) {
      console.error('Error deleting post:', error);
      throw error;
    }
  }, []);

  const toggleLikePost = useCallback(async (postId: string, userId: string) => {
    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      const isLiked = post.likedUsers?.includes(userId) || false;

      if (isLiked) {
        await unlikePost(postId, userId);
      } else {
        await likePost(postId, userId);
      }

      // Update local state optimistically
      setPosts(prevPosts =>
        prevPosts.map(p =>
          p.id === postId
            ? {
                ...p,
                likes: isLiked ? Math.max((p.likes || 0) - 1, 0) : (p.likes || 0) + 1,
                likedUsers: isLiked
                  ? (p.likedUsers || []).filter(id => id !== userId)
                  : [...(p.likedUsers || []), userId],
                isLikedByCurrentUser: !isLiked,
              }
            : p
        )
      );
    } catch (error) {
      console.error('Error toggling like:', error);
      throw error;
    }
  }, [posts]);

  const addCommentToPost = useCallback(async (postId: string, commentText: string, currentUser: User, parentId?: string, replyToUsername?: string) => {
    try {
      const commentData: any = {
        userId: currentUser.id,
        username: currentUser.username,
        avatarUrl: currentUser.avatarUrl,
        text: commentText,
      };

      // Only add optional fields if they have values
      if (parentId) {
        commentData.parentId = parentId;
      }
      if (replyToUsername) {
        commentData.replyToUsername = replyToUsername;
      }

      await firebaseAddCommentToPost(postId, commentData);

      // Refresh posts to get the updated comments
      await refreshPosts();
    } catch (error) {
      console.error('Error adding comment:', error);
      throw error;
    }
  }, [refreshPosts]);

  const deleteComment = useCallback(async (postId: string, commentId: string) => {
    try {
      await firebaseDeleteCommentFromPost(postId, commentId);

      // Refresh posts to get the updated comments
      await refreshPosts();
    } catch (error) {
      console.error('Error deleting comment:', error);
      throw error;
    }
  }, [refreshPosts]);

  // Helper function to find comment in nested structure
  const findCommentInTree = useCallback((comments: Comment[], commentId: string): Comment | null => {
    for (const comment of comments) {
      if (comment.id === commentId) {
        return comment;
      }
      if (comment.replies && comment.replies.length > 0) {
        const found = findCommentInTree(comment.replies, commentId);
        if (found) return found;
      }
    }
    return null;
  }, []);

  // Helper function to update comment in nested structure
  const updateCommentInTreeLocal = useCallback((comments: Comment[], commentId: string, updateFn: (comment: Comment) => Comment): Comment[] => {
    return comments.map(comment => {
      if (comment.id === commentId) {
        return updateFn(comment);
      }
      if (comment.replies && comment.replies.length > 0) {
        return {
          ...comment,
          replies: updateCommentInTreeLocal(comment.replies, commentId, updateFn)
        };
      }
      return comment;
    });
  }, []);

  const toggleLikeComment = useCallback(async (postId: string, commentId: string, userId: string) => {
    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      const comment = findCommentInTree(post.comments, commentId);
      if (!comment) return;

      const isLiked = comment.likedUsers?.includes(userId) || false;

      if (isLiked) {
        await unlikeComment(postId, commentId, userId);
      } else {
        await likeComment(postId, commentId, userId);
      }

      // Update local state optimistically
      setPosts(prevPosts =>
        prevPosts.map(p =>
          p.id === postId
            ? {
                ...p,
                comments: updateCommentInTreeLocal(p.comments, commentId, (c) => ({
                  ...c,
                  likes: isLiked ? Math.max((c.likes || 0) - 1, 0) : (c.likes || 0) + 1,
                  likedUsers: isLiked
                    ? (c.likedUsers || []).filter(id => id !== userId)
                    : [...(c.likedUsers || []), userId],
                  isLikedByCurrentUser: !isLiked,
                }))
              }
            : p
        )
      );
    } catch (error) {
      console.error('Error toggling comment like:', error);
      throw error;
    }
  }, [posts, findCommentInTree, updateCommentInTreeLocal]);

  // Helper function to migrate old comments to new structure
  const migrateCommentStructure = useCallback((comment: Comment): Comment => {
    return {
      ...comment,
      depth: comment.depth ?? 0,
      replies: comment.replies ?? [],
      // Ensure all required fields exist
      likes: comment.likes ?? 0,
      likedUsers: comment.likedUsers ?? [],
    };
  }, []);

  // Helper function to update comments recursively with current user's like status
  const updateCommentsWithCurrentUser = useCallback((comments: Comment[], currentUserId: string | null): Comment[] => {
    return comments.map(comment => {
      const migratedComment = migrateCommentStructure(comment);
      return {
        ...migratedComment,
        isLikedByCurrentUser: currentUserId ? (migratedComment.likedUsers || []).includes(currentUserId) : false,
        replies: migratedComment.replies ? updateCommentsWithCurrentUser(migratedComment.replies, currentUserId) : []
      };
    });
  }, [migrateCommentStructure]);

  const updatePostsWithCurrentUser = useCallback((currentUserId: string | null) => {
    setPosts(prevPosts =>
      prevPosts.map(post => ({
        ...post,
        isLikedByCurrentUser: currentUserId ? (post.likedUsers || []).includes(currentUserId) : false,
        comments: updateCommentsWithCurrentUser(post.comments, currentUserId)
      }))
    );
  }, [updateCommentsWithCurrentUser]);

  const contextValue = useMemo(() => ({
    posts,
    loading: loading || authLoading, // Show loading if either auth or posts are loading
    getPostById,
    addPost,
    editPost,
    deletePost,
    toggleLikePost,
    addCommentToPost,
    deleteComment,
    toggleLikeComment,
    refreshPosts,
    updatePostsWithCurrentUser,
  }), [posts, loading, authLoading, getPostById, addPost, editPost, deletePost, toggleLikePost, addCommentToPost, deleteComment, toggleLikeComment, refreshPosts, updatePostsWithCurrentUser]);


  return (
    <PostsContext.Provider value={contextValue}>
      {children}
    </PostsContext.Provider>
  );
};
import path from 'path';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      build: {
        // Optimize for production
        minify: 'terser',
        sourcemap: false,
        rollupOptions: {
          output: {
            manualChunks: {
              // Split vendor libraries into separate chunks for better caching
              vendor: ['react', 'react-dom'],
              router: ['react-router-dom'],
              firebase: ['firebase/app', 'firebase/auth', 'firebase/firestore', 'firebase/storage'],
              utils: ['uuid', 'date-fns']
            }
          }
        },
        // Increase chunk size warning limit
        chunkSizeWarningLimit: 1000,
        // Optimize assets
        assetsInlineLimit: 4096
      },
      // Optimize dev server
      server: {
        port: 3000,
        open: true
      },
      // Preview server config
      preview: {
        port: 4173,
        open: true
      }
    };
});

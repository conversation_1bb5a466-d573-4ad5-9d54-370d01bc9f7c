<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Firebase Setup - S3Kt0R-Gram</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        button {
            background-color: #00ff88;
            color: #000;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #00cc66;
        }
        .result {
            background-color: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #00ff88;
        }
        .error {
            border-left-color: #ff4444;
            background-color: #442222;
        }
        .success {
            border-left-color: #00ff88;
            background-color: #224422;
        }
    </style>
</head>
<body>
    <h1>🔧 Firebase Setup Test - S3Kt0R-Gram</h1>
    
    <div class="test-section">
        <h2>Authentication Test</h2>
        <button id="testAuth">Test Authentication</button>
        <div id="authResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Firestore Connection Test</h2>
        <button id="testFirestore">Test Firestore Connection</button>
        <div id="firestoreResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Posts Collection Test</h2>
        <button id="testPosts">Test Posts Query</button>
        <button id="createTestPost">Create Test Post</button>
        <div id="postsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Storage Test</h2>
        <button id="testStorage">Test Storage Connection</button>
        <div id="storageResult" class="result"></div>
    </div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithEmailAndPassword, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, collection, getDocs, addDoc, query, orderBy, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getStorage, ref, listAll } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js';

        const firebaseConfig = {
            apiKey: "AIzaSyBz12g2Ilty-HnKNCLq4rD9q0bMivjcGUM",
            authDomain: "s3kt0r-30ede.firebaseapp.com",
            projectId: "s3kt0r-30ede",
            storageBucket: "s3kt0r-30ede.firebasestorage.app",
            messagingSenderId: "246065925479",
            appId: "1:246065925479:web:4c7a0f2660fcc9880af3c2",
            measurementId: "G-1R3KWWKXBM"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);
        const storage = getStorage(app);

        let currentUser = null;

        // Monitor auth state
        onAuthStateChanged(auth, (user) => {
            currentUser = user;
            if (user) {
                document.getElementById('authResult').innerHTML = `
                    <div class="success">✅ Authenticated as: ${user.email}</div>
                `;
            } else {
                document.getElementById('authResult').innerHTML = `
                    <div>ℹ️ Not authenticated. Click "Test Authentication" to sign in.</div>
                `;
            }
        });

        // Test Authentication
        document.getElementById('testAuth').addEventListener('click', async () => {
            const resultDiv = document.getElementById('authResult');
            resultDiv.innerHTML = 'Testing authentication...';
            
            try {
                const userCredential = await signInWithEmailAndPassword(auth, '<EMAIL>', 'Tr1p$t0p3301');
                resultDiv.innerHTML = `
                    <div class="success">✅ Authentication successful!</div>
                    <div>User: ${userCredential.user.email}</div>
                    <div>UID: ${userCredential.user.uid}</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Authentication failed: ${error.message}</div>
                `;
            }
        });

        // Test Firestore
        document.getElementById('testFirestore').addEventListener('click', async () => {
            const resultDiv = document.getElementById('firestoreResult');
            resultDiv.innerHTML = 'Testing Firestore connection...';
            
            try {
                const usersSnapshot = await getDocs(collection(db, 'users'));
                resultDiv.innerHTML = `
                    <div class="success">✅ Firestore connection successful!</div>
                    <div>Found ${usersSnapshot.size} users in database</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Firestore connection failed: ${error.message}</div>
                `;
            }
        });

        // Test Posts
        document.getElementById('testPosts').addEventListener('click', async () => {
            const resultDiv = document.getElementById('postsResult');
            resultDiv.innerHTML = 'Testing posts query...';
            
            try {
                const postsQuery = query(collection(db, 'posts'), orderBy('timestamp', 'desc'));
                const postsSnapshot = await getDocs(postsQuery);
                const posts = postsSnapshot.docs
                    .filter(doc => !doc.data()._placeholder)
                    .map(doc => ({ id: doc.id, ...doc.data() }));
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Posts query successful!</div>
                    <div>Found ${posts.length} posts</div>
                    <div>Total documents: ${postsSnapshot.size}</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Posts query failed: ${error.message}</div>
                `;
            }
        });

        // Create Test Post
        document.getElementById('createTestPost').addEventListener('click', async () => {
            const resultDiv = document.getElementById('postsResult');
            
            if (!currentUser) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Please authenticate first before creating a test post</div>
                `;
                return;
            }
            
            resultDiv.innerHTML = 'Creating test post...';
            
            try {
                const testPost = {
                    userId: currentUser.uid,
                    username: 'Test User',
                    userAvatarUrl: 'https://picsum.photos/seed/test/100/100',
                    imageUrl: 'https://picsum.photos/seed/testpost/400/300',
                    caption: 'Test post created by Firebase setup test',
                    contentBody: 'This is a test post to verify that post creation is working correctly.',
                    tags: ['test', 'firebase', 'setup'],
                    isAnonymous: false,
                    timestamp: Timestamp.now().toDate().toISOString(),
                    likes: 0,
                    likedUsers: [],
                    comments: []
                };

                const docRef = await addDoc(collection(db, 'posts'), testPost);
                resultDiv.innerHTML = `
                    <div class="success">✅ Test post created successfully!</div>
                    <div>Post ID: ${docRef.id}</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Test post creation failed: ${error.message}</div>
                `;
            }
        });

        // Test Storage
        document.getElementById('testStorage').addEventListener('click', async () => {
            const resultDiv = document.getElementById('storageResult');
            resultDiv.innerHTML = 'Testing storage connection...';
            
            try {
                const storageRef = ref(storage);
                await listAll(storageRef);
                resultDiv.innerHTML = `
                    <div class="success">✅ Storage connection successful!</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Storage connection failed: ${error.message}</div>
                `;
            }
        });
    </script>
</body>
</html>

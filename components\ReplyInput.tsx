import React, { useState, useRef, useEffect } from 'react';
import { User } from '../types';

interface ReplyInputProps {
  currentUser: User;
  onSubmit: (text: string) => Promise<void>;
  onCancel: () => void;
  replyToUsername: string;
  placeholder?: string;
}

const ReplyInput: React.FC<ReplyInputProps> = ({
  currentUser,
  onSubmit,
  onCancel,
  replyToUsername,
  placeholder = "Write a reply..."
}) => {
  const [replyText, setReplyText] = useState(`@${replyToUsername} `);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Focus the input and position cursor after the @username
    if (inputRef.current) {
      inputRef.current.focus();
      inputRef.current.setSelectionRange(replyText.length, replyText.length);
    }
  }, [replyText]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (replyText.trim() && !isSubmitting) {
      setIsSubmitting(true);
      try {
        await onSubmit(replyText.trim());
        setReplyText(`@${replyToUsername} `);
        onCancel(); // Close the reply input after successful submission
      } catch (error) {
        console.error('Error submitting reply:', error);
        alert('Failed to add reply. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onCancel();
    }
  };

  return (
    <div className="mt-2 p-3 bg-neutral-surface rounded-md border border-brand-primary animate-slide-down">
      <form onSubmit={handleSubmit} className="flex items-center space-x-2">
        <img 
          src={currentUser.avatarUrl} 
          alt={currentUser.username} 
          className="w-6 h-6 rounded-full flex-shrink-0" 
        />
        <input
          ref={inputRef}
          type="text"
          value={replyText}
          onChange={(e) => setReplyText(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="flex-grow bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-1 focus:ring-brand-primary focus:border-brand-primary text-sm"
          disabled={isSubmitting}
        />
        <div className="flex space-x-2">
          <button 
            type="submit" 
            disabled={!replyText.trim() || isSubmitting}
            className="bg-brand-primary hover:bg-brand-secondary disabled:bg-neutral-muted text-white px-3 py-2 rounded-md text-sm font-medium transition-colors disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Posting...' : 'Reply'}
          </button>
          <button 
            type="button" 
            onClick={onCancel}
            disabled={isSubmitting}
            className="bg-neutral-muted hover:bg-neutral-border text-neutral-100 px-3 py-2 rounded-md text-sm font-medium transition-colors disabled:cursor-not-allowed"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default ReplyInput;

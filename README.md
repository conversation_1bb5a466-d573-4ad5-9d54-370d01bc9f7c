# S3KT0R

A decentralized-themed social media platform built with React, TypeScript, and Firebase.

## Features

- User authentication and profiles
- Post creation and sharing
- Real-time commenting system
- Threaded comments and replies
- Instagram-style follow functionality
- Messaging/instant messaging system
- Admin dashboard with analytics
- Responsive design with anarcho/cyberpunk aesthetic
- Matrix rain background animation

## Tech Stack

- **Frontend**: React 19, TypeScript, Vite
- **Backend**: Firebase (Auth, Firestore, Storage)
- **Styling**: Tailwind CSS with custom anarcho theme
- **Routing**: React Router DOM
- **Charts**: Chart.js
- **Deployment**: Netlify (auto-deploy from GitHub)

## Getting Started

### Prerequisites

- Node.js 18 or higher
- npm or yarn
- Firebase project

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd s3kt0r
```

2. Install dependencies:
```bash
npm install
```

3. Set up Firebase configuration in `firebase.ts`

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

## Deployment

This project is configured for automatic deployment on Netlify:

1. **Netlify Configuration**: `netlify.toml` contains all build and deployment settings
2. **SPA Support**: `public/_redirects` ensures React Router works correctly
3. **Build Optimization**: Vite config optimized for production builds
4. **Auto-deploy**: Connected to GitHub for automatic deployments on push

### Manual Deployment

To deploy manually:

```bash
npm run build
# Upload the `dist` folder to your hosting provider
```

## Firebase Setup

See `FIREBASE_SETUP_GUIDE.md` for detailed Firebase configuration instructions.

## Project Structure

```
s3kt0r/
├── components/          # Reusable React components
├── pages/              # Page components
├── contexts/           # React contexts (Auth, Posts)
├── hooks/              # Custom React hooks
├── services/           # Firebase services
├── utils/              # Utility functions
├── public/             # Static assets
├── functions/          # Firebase Cloud Functions
└── scripts/            # Setup and utility scripts
```

## Environment Variables

For local development, create a `.env.local` file:

```env
GEMINI_API_KEY=your_gemini_api_key_here
```

## License

This project is private and proprietary.

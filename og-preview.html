<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S3KT0R - OG Card Preview</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f2f5;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #1c1e21;
            margin-bottom: 30px;
        }
        
        .platform {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .platform-header {
            background: #4267B2;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        
        .twitter .platform-header {
            background: #1DA1F2;
        }
        
        .linkedin .platform-header {
            background: #0077B5;
        }
        
        .discord .platform-header {
            background: #5865F2;
        }
        
        .og-card {
            border: 1px solid #dadde1;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px;
            max-width: 500px;
        }
        
        .og-image {
            width: 100%;
            height: 260px;
            background: #00DC82;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .og-image img {
            max-width: 200px;
            max-height: 200px;
            object-fit: contain;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
        }
        
        .og-content {
            padding: 15px;
            background: white;
        }
        
        .og-title {
            font-size: 16px;
            font-weight: bold;
            color: #1c1e21;
            margin-bottom: 5px;
            line-height: 1.3;
        }
        
        .og-description {
            font-size: 14px;
            color: #65676b;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .og-url {
            font-size: 12px;
            color: #8a8d91;
            text-transform: uppercase;
        }
        
        .twitter-card {
            border: 1px solid #e1e8ed;
            border-radius: 14px;
            overflow: hidden;
            margin: 20px;
            max-width: 500px;
        }
        
        .twitter-card .og-image {
            height: 250px;
        }
        
        .preview-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
        }
        
        .logo-placeholder {
            width: 120px;
            height: 120px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: #00DC82;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            border: 3px solid rgba(255,255,255,0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 S3KT0R - OG Card Preview</h1>
        
        <div class="preview-note">
            <strong>📋 Preview Note:</strong> This shows how your S3KT0R platform will appear when shared on social media. 
            Your actual logo.png will replace the placeholder below. The OG meta tags have been added to your index.html file.
        </div>

        <!-- Facebook/Meta Preview -->
        <div class="platform facebook">
            <div class="platform-header">📘 Facebook / Meta</div>
            <div class="og-card">
                <div class="og-image">
                    <img src="./public/img/logo.png" alt="S3KT0R Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="logo-placeholder" style="display: none;">S3KT0R</div>
                </div>
                <div class="og-content">
                    <div class="og-title">S3KT0R - Make Information De-Centralized Again</div>
                    <div class="og-description">A decentralized-themed social media platform where users can share posts, comment, and interact. The truth is in the code. Choose your path.</div>
                    <div class="og-url">s3kt0r.com</div>
                </div>
            </div>
        </div>

        <!-- Twitter Preview -->
        <div class="platform twitter">
            <div class="platform-header">🐦 Twitter / X</div>
            <div class="twitter-card">
                <div class="og-image">
                    <img src="./public/img/logo.png" alt="S3KT0R Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="logo-placeholder" style="display: none;">S3KT0R</div>
                </div>
                <div class="og-content">
                    <div class="og-title">S3KT0R - Make Information De-Centralized Again</div>
                    <div class="og-description">A decentralized-themed social media platform where users can share posts, comment, and interact. The truth is in the code. Choose your path.</div>
                    <div class="og-url">s3kt0r.com</div>
                </div>
            </div>
        </div>

        <!-- LinkedIn Preview -->
        <div class="platform linkedin">
            <div class="platform-header">💼 LinkedIn</div>
            <div class="og-card">
                <div class="og-image">
                    <img src="./public/img/logo.png" alt="S3KT0R Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="logo-placeholder" style="display: none;">S3KT0R</div>
                </div>
                <div class="og-content">
                    <div class="og-title">S3KT0R - Make Information De-Centralized Again</div>
                    <div class="og-description">A decentralized-themed social media platform where users can share posts, comment, and interact. The truth is in the code. Choose your path.</div>
                    <div class="og-url">s3kt0r.com</div>
                </div>
            </div>
        </div>

        <!-- Discord Preview -->
        <div class="platform discord">
            <div class="platform-header">🎮 Discord</div>
            <div class="og-card">
                <div class="og-image">
                    <img src="./public/img/logo.png" alt="S3KT0R Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="logo-placeholder" style="display: none;">S3KT0R</div>
                </div>
                <div class="og-content">
                    <div class="og-title">S3KT0R - Make Information De-Centralized Again</div>
                    <div class="og-description">A decentralized-themed social media platform where users can share posts, comment, and interact. The truth is in the code. Choose your path.</div>
                    <div class="og-url">s3kt0r.com</div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #65676b;">
            <p><strong>✅ OG Meta Tags Status:</strong> Successfully added to index.html</p>
            <p><strong>🎯 Platforms Supported:</strong> Facebook, Twitter, LinkedIn, Discord, WhatsApp, Telegram, and more</p>
            <p><strong>🖼️ Image:</strong> Using your logo.png from public/img/</p>
        </div>
    </div>
</body>
</html>

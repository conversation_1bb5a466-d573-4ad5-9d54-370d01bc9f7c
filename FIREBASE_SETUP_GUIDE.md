# 🔥 Firebase Setup Guide for S3KT0R

## 📋 Current Status

Your Firebase project `s3kt0r-30ede` is configured with the following services:
- ✅ Authentication (Email/Password)
- ✅ Firestore Database
- ✅ Cloud Storage
- ✅ Security Rules
- ✅ Indexes

## 🔧 Recent Fixes Applied

### 1. Fixed Post Queries
- **Issue**: Complex query with non-existent `_placeholder` field was causing failures
- **Fix**: Simplified to use basic timestamp ordering with client-side filtering
- **Location**: `services/firebaseService.ts` - `getAllPosts()` function

### 2. Updated Firestore Indexes
- **Added**: Basic timestamp index for posts
- **Added**: Anonymous posts filtering index
- **Enhanced**: Existing user and tag-based indexes
- **Location**: `firestore.indexes.json`

### 3. Enhanced Security Rules
- **Improved**: Anonymous post creation and management rules
- **Added**: Support for `actualUserId` field in anonymous posts
- **Enhanced**: Post update/delete permissions for anonymous posts
- **Location**: `firestore.rules`

### 4. Enhanced Post Creation
- **Added**: Automatic `likedUsers` array initialization
- **Improved**: Error logging and handling
- **Location**: `services/firebaseService.ts` - `createPost()` function

## 📊 Required Collections Structure

### Users Collection (`/users/{userId}`)
```javascript
{
  id: string,
  username: string,
  avatarUrl: string,
  isActive: boolean,
  isPendingApproval: boolean,
  isAdmin?: boolean,
  bio?: string
}
```

### Posts Collection (`/posts/{postId}`)
```javascript
{
  id: string,
  userId: string,
  username: string,
  userAvatarUrl: string,
  imageUrl: string,
  caption: string,
  contentBody?: string,
  likes: number,
  likedUsers: string[],
  comments: Comment[],
  timestamp: string,
  tags?: string[],
  isAnonymous?: boolean,
  actualUserId?: string
}
```

### Admin Messages Collection (`/adminMessages/{messageId}`)
```javascript
{
  id: string,
  senderName: string,
  senderEmail?: string,
  subject: string,
  message: string,
  timestamp: string,
  isRead: boolean
}
```

### Config Collection (`/config/loginScreen`)
```javascript
{
  message: string,
  imageUrl: string,
  imageOverlayOpacity?: number
}
```

## 🔐 Security Rules Summary

### Posts Rules
- **Read**: Any authenticated user
- **Create**: Active users only (including anonymous posts)
- **Update**: Post owner or admin
- **Delete**: Post owner or admin

### Users Rules
- **Read**: Own profile, active users, or admin
- **Update**: Own profile only (non-admin fields)
- **Create/Delete**: Admin only

### Storage Rules
- **Avatars**: Users can upload their own
- **Posts**: Users can upload to their own folder
- **Admin**: Admin-only uploads

## 📈 Required Indexes

1. **Posts by timestamp**: `timestamp DESC`
2. **Posts by user**: `userId ASC, timestamp DESC`
3. **Posts by tags**: `tags CONTAINS, timestamp DESC`
4. **Posts by anonymous status**: `isAnonymous ASC, timestamp DESC`
5. **Users by status**: `isActive ASC, isPendingApproval ASC`
6. **Admin messages**: `isRead ASC, timestamp DESC`

## 🧪 Testing Your Setup

1. **Open the test file**: `testFirebaseSetup.html` in your browser
2. **Run tests in order**:
   - Test Authentication (signs in as admin)
   - Test Firestore Connection
   - Test Posts Query
   - Create Test Post
   - Test Storage Connection

## 🚀 Deployment Commands

### Deploy Firestore Rules
```bash
firebase deploy --only firestore:rules
```

### Deploy Firestore Indexes
```bash
firebase deploy --only firestore:indexes
```

### Deploy Storage Rules
```bash
firebase deploy --only storage
```

### Deploy Everything
```bash
firebase deploy
```

## 🔍 Common Issues & Solutions

### Issue: "Missing or insufficient permissions"
- **Cause**: User not authenticated or not active
- **Solution**: Ensure user is signed in and `isActive: true`

### Issue: "Index not found"
- **Cause**: Firestore indexes not deployed
- **Solution**: Run `firebase deploy --only firestore:indexes`

### Issue: "Posts not loading"
- **Cause**: Query structure or permissions
- **Solution**: Check browser console for specific errors

### Issue: "Anonymous posts not working"
- **Cause**: Security rules or missing `actualUserId`
- **Solution**: Ensure `actualUserId` is set for anonymous posts

## 📝 Admin Account

- **Email**: `<EMAIL>`
- **Password**: `Tr1p$t0p3301`
- **Status**: Admin with full permissions

## 🎯 Next Steps

1. **Deploy the updated rules and indexes**:
   ```bash
   firebase deploy --only firestore
   ```

2. **Test the setup** using `testFirebaseSetup.html`

3. **Initialize collections** using `initializeCollections.html` if needed

4. **Test post creation** in your main application

Your Firebase setup should now be fully functional for saving and fetching posts! 🎉

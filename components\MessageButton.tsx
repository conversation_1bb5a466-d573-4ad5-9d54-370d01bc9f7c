import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { findOrCreateConversation } from '../services/firebaseService';

interface MessageButtonProps {
  currentUserId: string;
  targetUserId: string;
  targetUsername: string;
}

const MessageButton: React.FC<MessageButtonProps> = ({
  currentUserId,
  targetUserId,
  targetUsername
}) => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleMessage = async () => {
    if (loading) return;

    setLoading(true);
    try {
      const conversationId = await findOrCreateConversation(currentUserId, targetUserId);
      navigate('/messages', { state: { conversationId } });
    } catch (error) {
      console.error('Error creating conversation:', error);
      alert('Failed to start conversation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Don't show button if user is trying to message themselves
  if (currentUserId === targetUserId) {
    return null;
  }

  return (
    <button
      onClick={handleMessage}
      disabled={loading}
      className="px-4 py-2 rounded-md font-semibold text-sm bg-neutral-base border border-neutral-border text-neutral-100 hover:bg-neutral-border hover:text-brand-primary transition-all duration-200 hover-scale hover-glow disabled:opacity-50 disabled:cursor-not-allowed"
      title={`Send message to ${targetUsername}`}
    >
      {loading ? (
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border border-current border-t-transparent rounded-full animate-spin"></div>
          <span>Loading...</span>
        </div>
      ) : (
        <div className="flex items-center space-x-2">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          <span>Message</span>
        </div>
      )}
    </button>
  );
};

export default MessageButton;

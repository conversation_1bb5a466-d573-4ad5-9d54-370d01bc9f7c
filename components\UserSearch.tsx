import React, { useState, useEffect } from 'react';
import { User } from '../types';
import { searchUsersForMessaging } from '../services/firebaseService';

interface UserSearchProps {
  onSelectUser: (userId: string) => void;
  onClose: () => void;
  currentUserId: string;
}

const UserSearch: React.FC<UserSearchProps> = ({
  onSelectUser,
  onClose,
  currentUserId
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  // Search users when search term changes
  useEffect(() => {
    const searchUsers = async () => {
      if (searchTerm.trim().length < 2) {
        setUsers([]);
        setHasSearched(false);
        return;
      }

      setLoading(true);
      try {
        const results = await searchUsersForMessaging(searchTerm, currentUserId);
        setUsers(results);
        setHasSearched(true);
      } catch (error) {
        console.error('Error searching users:', error);
        setUsers([]);
      } finally {
        setLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchUsers, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchTerm, currentUserId]);

  const handleUserSelect = (userId: string) => {
    onSelectUser(userId);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-neutral-surface border border-neutral-border rounded-lg w-full max-w-md max-h-96 cyber-border animate-scale-in">
        {/* Header */}
        <div className="p-4 border-b border-neutral-border">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-neutral-100 animate-text-glow">
              Start New Conversation
            </h3>
            <button
              onClick={onClose}
              className="text-neutral-muted hover:text-neutral-100 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Search Input */}
        <div className="p-4">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search users by username..."
            className="w-full px-4 py-2 bg-neutral-base border border-neutral-border rounded-md text-neutral-100 placeholder-neutral-muted focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent transition-all duration-200 hover-glow"
            autoFocus
          />
        </div>

        {/* Results */}
        <div className="max-h-64 overflow-y-auto">
          {loading && (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-primary mx-auto"></div>
              <p className="text-neutral-muted text-sm mt-2">Searching...</p>
            </div>
          )}

          {!loading && searchTerm.trim().length < 2 && (
            <div className="p-4 text-center">
              <div className="text-4xl mb-2">🔍</div>
              <p className="text-neutral-muted text-sm">
                Type at least 2 characters to search for users
              </p>
            </div>
          )}

          {!loading && hasSearched && users.length === 0 && searchTerm.trim().length >= 2 && (
            <div className="p-4 text-center">
              <div className="text-4xl mb-2">😔</div>
              <p className="text-neutral-muted text-sm">
                No users found matching "{searchTerm}"
              </p>
            </div>
          )}

          {!loading && users.length > 0 && (
            <div className="divide-y divide-neutral-border">
              {users.map((user) => (
                <div
                  key={user.id}
                  onClick={() => handleUserSelect(user.id)}
                  className="p-4 hover:bg-neutral-base/50 cursor-pointer transition-all duration-200 hover-scale"
                >
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <img
                        src={user.avatarUrl}
                        alt={user.username}
                        className="w-10 h-10 rounded-full border-2 border-brand-primary/50"
                      />
                      {user.isActive && (
                        <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-neutral-surface animate-pulse"></div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-neutral-100">
                        {user.username}
                      </h4>
                      <p className="text-sm text-neutral-muted">
                        {user.isActive ? 'Active' : 'Offline'}
                      </p>
                    </div>
                    <div className="text-brand-primary">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserSearch;

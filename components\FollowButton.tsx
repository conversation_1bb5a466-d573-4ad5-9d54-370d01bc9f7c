import React, { useState, useEffect } from 'react';
import { followUser, unfollowUser, isFollowing } from '../services/firebaseService';

interface FollowButtonProps {
  currentUserId: string;
  targetUserId: string;
  onFollowChange?: (isFollowing: boolean) => void;
}

const FollowButton: React.FC<FollowButtonProps> = ({
  currentUserId,
  targetUserId,
  onFollowChange
}) => {
  const [following, setFollowing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    const checkFollowStatus = async () => {
      try {
        const followStatus = await isFollowing(currentUserId, targetUserId);
        setFollowing(followStatus);
      } catch (error) {
        console.error('Error checking follow status:', error);
      } finally {
        setLoading(false);
      }
    };

    if (currentUserId && targetUserId && currentUserId !== targetUserId) {
      checkFollowStatus();
    } else {
      setLoading(false);
    }
  }, [currentUserId, targetUserId]);

  const handleFollowToggle = async () => {
    if (actionLoading) return;

    setActionLoading(true);
    try {
      if (following) {
        await unfollowUser(currentUserId, targetUserId);
        setFollowing(false);
        onFollowChange?.(false);
      } else {
        await followUser(currentUserId, targetUserId);
        setFollowing(true);
        onFollowChange?.(true);
      }
    } catch (error) {
      console.error('Error toggling follow:', error);
      alert('Failed to update follow status. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  // Don't show button if user is trying to follow themselves
  if (currentUserId === targetUserId) {
    return null;
  }

  if (loading) {
    return (
      <div className="w-20 h-8 bg-neutral-base rounded-md animate-pulse"></div>
    );
  }

  return (
    <button
      onClick={handleFollowToggle}
      disabled={actionLoading}
      className={`px-4 py-2 rounded-md font-semibold text-sm transition-all duration-200 hover-scale hover-glow disabled:opacity-50 disabled:cursor-not-allowed ${
        following
          ? 'bg-neutral-base border border-neutral-border text-neutral-100 hover:bg-red-500/20 hover:text-red-400 hover:border-red-400'
          : 'bg-brand-primary text-white hover:bg-brand-secondary'
      }`}
    >
      {actionLoading ? (
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border border-current border-t-transparent rounded-full animate-spin"></div>
          <span>{following ? 'Unfollowing...' : 'Following...'}</span>
        </div>
      ) : (
        following ? 'Unfollow' : 'Follow'
      )}
    </button>
  );
};

export default FollowButton;

import React, { useState, useRef, useEffect } from 'react';
import { sendMessage } from '../services/firebaseService';

interface MessageInputProps {
  conversationId: string;
  currentUserId: string;
  placeholder?: string;
}

const MessageInput: React.FC<MessageInputProps> = ({
  conversationId,
  currentUserId,
  placeholder = 'Type a message...'
}) => {
  const [message, setMessage] = useState('');
  const [sending, setSending] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  const handleSend = async () => {
    if (!message.trim() || sending) return;

    const messageContent = message.trim();
    setMessage('');
    setSending(true);

    try {
      await sendMessage(conversationId, currentUserId, messageContent);
    } catch (error) {
      console.error('Error sending message:', error);
      // Restore message on error
      setMessage(messageContent);
    } finally {
      setSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
  };

  return (
    <div className="p-4">
      <div className="flex items-end space-x-3">
        {/* Message Input */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={sending}
            rows={1}
            className="w-full px-4 py-3 bg-neutral-base border border-neutral-border rounded-2xl text-neutral-100 placeholder-neutral-muted resize-none focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent transition-all duration-200 hover-glow max-h-32 overflow-y-auto"
            style={{ minHeight: '48px' }}
          />
          
          {/* Character count indicator (optional) */}
          {message.length > 500 && (
            <div className="absolute bottom-2 right-2 text-xs text-neutral-muted">
              {message.length}/1000
            </div>
          )}
        </div>

        {/* Send Button */}
        <button
          onClick={handleSend}
          disabled={!message.trim() || sending}
          className={`p-3 rounded-full transition-all duration-200 hover-scale ${
            message.trim() && !sending
              ? 'bg-brand-primary hover:bg-brand-secondary text-white hover-glow animate-pulse-glow'
              : 'bg-neutral-border text-neutral-muted cursor-not-allowed'
          }`}
          title="Send message"
        >
          {sending ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          ) : (
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
              />
            </svg>
          )}
        </button>
      </div>

      {/* Typing indicator placeholder */}
      <div className="mt-2 h-4">
        {/* Future: Add typing indicator here */}
      </div>
    </div>
  );
};

export default MessageInput;

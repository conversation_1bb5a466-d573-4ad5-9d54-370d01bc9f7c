{"indexes": [{"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tags", "arrayConfig": "CONTAINS"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isAnonymous", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "isPendingApproval", "order": "ASCENDING"}]}, {"collectionGroup": "adminMessages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isRead", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "conversations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "arrayConfig": "CONTAINS"}, {"fieldPath": "lastActivity", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "conversationId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "conversationId", "order": "ASCENDING"}, {"fieldPath": "senderId", "order": "ASCENDING"}, {"fieldPath": "isRead", "order": "ASCENDING"}]}], "fieldOverrides": []}
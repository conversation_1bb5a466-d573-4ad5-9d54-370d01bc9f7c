<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Utils Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: #fff; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #2d5a2d; }
        .error { background: #5a2d2d; }
        input { width: 100%; padding: 10px; margin: 10px 0; background: #333; color: #fff; border: 1px solid #555; }
        button { padding: 10px 20px; background: #00dc82; color: #000; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Video Utils Test</h1>
    <input type="text" id="videoUrl" placeholder="Enter video URL to test..." value="https://www.youtube.com/watch?v=dQw4w9WgXcQ">
    <button onclick="testVideoUrl()">Test Video URL</button>
    <div id="results"></div>

    <script type="module">
        // Video URL utilities for embedding and validation
        const getYouTubeVideoId = (url) => {
            const patterns = [
                /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
                /youtube\.com\/v\/([^&\n?#]+)/,
                /youtube\.com\/watch\?.*v=([^&\n?#]+)/
            ];
            
            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match) return match[1];
            }
            return null;
        };

        const getVimeoVideoId = (url) => {
            const match = url.match(/vimeo\.com\/(?:video\/)?(\d+)/);
            return match ? match[1] : null;
        };

        const getDailymotionVideoId = (url) => {
            const match = url.match(/dailymotion\.com\/video\/([^_]+)/);
            return match ? match[1] : null;
        };

        const getVideoInfo = (url) => {
            if (!url) return null;

            // YouTube
            const youtubeId = getYouTubeVideoId(url);
            if (youtubeId) {
                return {
                    embedUrl: `https://www.youtube.com/embed/${youtubeId}`,
                    thumbnailUrl: `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg`,
                    platform: 'youtube',
                    videoId: youtubeId
                };
            }

            // Vimeo
            const vimeoId = getVimeoVideoId(url);
            if (vimeoId) {
                return {
                    embedUrl: `https://player.vimeo.com/video/${vimeoId}`,
                    thumbnailUrl: `https://vumbnail.com/${vimeoId}.jpg`,
                    platform: 'vimeo',
                    videoId: vimeoId
                };
            }

            // Dailymotion
            const dailymotionId = getDailymotionVideoId(url);
            if (dailymotionId) {
                return {
                    embedUrl: `https://www.dailymotion.com/embed/video/${dailymotionId}`,
                    thumbnailUrl: `https://www.dailymotion.com/thumbnail/video/${dailymotionId}`,
                    platform: 'dailymotion',
                    videoId: dailymotionId
                };
            }

            return null;
        };

        window.testVideoUrl = () => {
            const url = document.getElementById('videoUrl').value;
            const results = document.getElementById('results');
            
            const videoInfo = getVideoInfo(url);
            
            if (videoInfo) {
                results.innerHTML = `
                    <div class="test-result success">
                        <h3>✅ Valid Video URL</h3>
                        <p><strong>Platform:</strong> ${videoInfo.platform}</p>
                        <p><strong>Video ID:</strong> ${videoInfo.videoId}</p>
                        <p><strong>Embed URL:</strong> ${videoInfo.embedUrl}</p>
                        <p><strong>Thumbnail URL:</strong> ${videoInfo.thumbnailUrl}</p>
                        <img src="${videoInfo.thumbnailUrl}" alt="Thumbnail" style="max-width: 200px; margin-top: 10px;">
                    </div>
                `;
            } else {
                results.innerHTML = `
                    <div class="test-result error">
                        <h3>❌ Invalid Video URL</h3>
                        <p>The URL is not recognized as a valid YouTube, Vimeo, or Dailymotion link.</p>
                    </div>
                `;
            }
        };

        // Test some URLs on load
        const testUrls = [
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'https://youtu.be/dQw4w9WgXcQ',
            'https://vimeo.com/123456789',
            'https://www.dailymotion.com/video/x123456',
            'https://invalid-url.com'
        ];

        console.log('Testing video URLs:');
        testUrls.forEach(url => {
            const info = getVideoInfo(url);
            console.log(`${url} -> ${info ? `${info.platform} (${info.videoId})` : 'Invalid'}`);
        });
    </script>
</body>
</html>

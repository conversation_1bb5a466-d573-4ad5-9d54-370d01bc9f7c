import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';
import { Post } from '../types';
import { uploadImage } from '../services/firebaseService';
import VideoUrlModal from '../components/VideoUrlModal';
import VideoIcon from '../components/icons/VideoIcon';
import ImageUploadIcon from '../components/icons/ImageUploadIcon';
import { getVideoInfo } from '../utils/videoUtils';

const EditPostPage: React.FC = () => {
  const { postId } = useParams<{ postId: string }>();
  const { currentUser, isAdmin } = useAuth();
  const { getPostById, editPost } = usePosts();
  const navigate = useNavigate();

  const [postToEdit, setPostToEdit] = useState<Post | null | undefined>(null);

  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [videoUrl, setVideoUrl] = useState('');

  const [caption, setCaption] = useState('');
  const [contentBody, setContentBody] = useState('');
  const [tags, setTags] = useState('');

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);


  useEffect(() => {
    if (!postId) {
      navigate('/');
      return;
    }
    const foundPost = getPostById(postId);
    setPostToEdit(foundPost);
    setIsLoading(false);

    if (foundPost) {
      if (!currentUser || (currentUser.id !== foundPost.userId && !isAdmin)) {
        setError("You don't have permission to edit this post.");
        return;
      }

      // Set video URL if it exists
      setVideoUrl(foundPost.videoUrl || '');

      // Handle image preview for uploaded images
      if (foundPost.imageUrl && foundPost.imageUrl.startsWith('data:image')) {
        setImagePreview(foundPost.imageUrl);
      }

      setCaption(foundPost.caption);
      setContentBody(foundPost.contentBody || '');
      setTags(foundPost.tags?.join(', ') || '');
    } else {
      setError("Post not found.");
    }
  }, [postId, getPostById, currentUser, isAdmin, navigate]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      setVideoUrl(''); // Clear video when image is selected
      const reader = new FileReader();
      reader.onloadend = () => {
        const dataUrl = reader.result as string;
        setImagePreview(dataUrl);
      };
      reader.readAsDataURL(file);
    } else {
      setImageFile(null);
      setImagePreview(null);
    }
  };

  const handleVideoUrlConfirm = (url: string) => {
    setVideoUrl(url);
    setImageFile(null); // Clear image when video is selected
    setImagePreview(null);
  };

  const handleImageUploadClick = () => {
    const fileInput = document.getElementById('imageUpload') as HTMLInputElement;
    fileInput?.click();
  };


  if (isLoading) {
    return <div className="text-center text-neutral-100 py-10">Loading post...</div>;
  }

  if (error) {
     return (
      <div className="max-w-md mx-auto mt-10 p-6 bg-neutral-surface rounded-lg shadow-xl border border-neutral-border text-center">
        <p className="text-lg text-accent-error">{error}</p>
        <button onClick={() => navigate('/')} className="mt-4 bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2 px-4 rounded-md transition-colors">
          Go Home
        </button>
      </div>
    );
  }

  if (!postToEdit) {
    return (
        <div className="max-w-md mx-auto mt-10 p-6 bg-neutral-surface rounded-lg shadow-xl border border-neutral-border text-center">
          <p className="text-lg text-neutral-100">Post not found or you do not have permission to edit it.</p>
        </div>
      );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser || !postToEdit) {
        alert("Error: User or post data missing.");
        return;
    }
    if (currentUser.id !== postToEdit.userId && !isAdmin) {
        alert("You don't have permission to edit this post.");
        return;
    }

    // Validate that we have either media OR text content
    if (!imageFile && !videoUrl.trim() && !postToEdit.imageUrl && !postToEdit.videoUrl && !caption.trim() && !contentBody.trim()) {
      alert("Please add either media (image/video) or text content (caption/body) to your post.");
      return;
    }

    setIsUploading(true);
    try {
      let finalImageUrl = postToEdit.imageUrl;

      // If uploading a new file, upload to Firebase Storage first
      if (imageFile) {
        const timestamp = Date.now();
        const fileName = `posts/${currentUser.id}/${timestamp}_${imageFile.name}`;
        finalImageUrl = await uploadImage(imageFile, fileName);
      } else if (videoUrl && videoUrl !== postToEdit.videoUrl) {
        // For video posts, use the video thumbnail as the image
        const videoInfo = getVideoInfo(videoUrl);
        if (videoInfo) {
          finalImageUrl = videoInfo.thumbnailUrl;
        }
      }

      await editPost(postToEdit.id, {
        imageUrl: finalImageUrl,
        videoUrl: videoUrl || undefined,
        caption,
        contentBody,
        tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
      });
      navigate('/');
    } catch (error) {
      console.error('Error updating post:', error);
      alert('Failed to update post. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="max-w-xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <h1 className="text-3xl font-bold text-brand-primary mb-8 text-center">Edit Post</h1>
      <form onSubmit={handleSubmit} className="space-y-6 bg-neutral-surface p-6 sm:p-8 rounded-lg shadow-2xl border border-neutral-border">

        {/* Media Preview Section */}
        {(imagePreview || videoUrl || (postToEdit && (postToEdit.imageUrl || postToEdit.videoUrl))) && (
          <div>
            <label className="block text-sm font-medium text-neutral-300 mb-2">Current Media</label>
            {imagePreview && (
              <div className="relative">
                <img src={imagePreview} alt="Preview" className="max-h-48 w-auto rounded-md border border-neutral-border" />
                <button
                  type="button"
                  onClick={() => {
                    setImageFile(null);
                    setImagePreview(null);
                  }}
                  className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 transition-colors duration-200"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}
            {videoUrl && (
              <div className="relative">
                <div className="bg-neutral-base border border-neutral-border rounded-md p-4">
                  <div className="flex items-center space-x-3">
                    <VideoIcon className="w-8 h-8 text-brand-primary" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-neutral-200">Video Link</p>
                      <p className="text-xs text-neutral-muted truncate">{videoUrl}</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => setVideoUrl('')}
                      className="bg-red-600 hover:bg-red-700 text-white rounded-full p-1 transition-colors duration-200"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            )}
            {!imagePreview && !videoUrl && postToEdit && (postToEdit.imageUrl || postToEdit.videoUrl) && (
              <div className="bg-neutral-base border border-neutral-border rounded-md p-4">
                <div className="flex items-center space-x-3">
                  {postToEdit.videoUrl ? (
                    <VideoIcon className="w-8 h-8 text-brand-primary" />
                  ) : (
                    <svg className="w-8 h-8 text-brand-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  )}
                  <div className="flex-1">
                    <p className="text-sm font-medium text-neutral-200">
                      {postToEdit.videoUrl ? 'Current Video' : 'Current Image'}
                    </p>
                    <p className="text-xs text-neutral-muted truncate">
                      {postToEdit.videoUrl || 'Uploaded image'}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        <div>
          <label htmlFor="caption" className="block text-sm font-medium text-neutral-300 mb-1">
            Caption (Short Summary)
          </label>
          <input
            id="caption"
            value={caption}
            onChange={(e) => setCaption(e.target.value)}
            maxLength={150}
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        <div>
          <label htmlFor="contentBody" className="block text-sm font-medium text-neutral-300 mb-1">
            Post Body
          </label>
          <textarea
            id="contentBody"
            value={contentBody}
            onChange={(e) => setContentBody(e.target.value)}
            rows={6}
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        <div>
          <label htmlFor="tags" className="block text-sm font-medium text-neutral-300 mb-1">
            Tags (comma-separated)
          </label>
          <input
            type="text"
            id="tags"
            value={tags}
            onChange={(e) => setTags(e.target.value)}
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        {/* Bottom Action Bar */}
        <div className="flex items-center space-x-3">
          {/* Media Icons */}
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={handleImageUploadClick}
              className="flex items-center justify-center w-10 h-10 bg-neutral-base hover:bg-brand-primary/20 border border-neutral-border hover:border-brand-primary text-neutral-300 hover:text-brand-primary rounded-md transition-all duration-200 hover-scale"
              title="Upload Image"
            >
              <ImageUploadIcon className="w-5 h-5" />
            </button>
            <button
              type="button"
              onClick={() => setShowVideoModal(true)}
              className="flex items-center justify-center w-10 h-10 bg-neutral-base hover:bg-brand-primary/20 border border-neutral-border hover:border-brand-primary text-neutral-300 hover:text-brand-primary rounded-md transition-all duration-200 hover-scale"
              title="Add Video Link"
            >
              <VideoIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Save Button */}
          <button
            type="submit"
            disabled={isUploading}
            className="flex-1 bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2.5 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-surface focus:ring-brand-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isUploading ? 'Saving Changes...' : 'Save Changes'}
          </button>
        </div>

        {/* Hidden File Input */}
        <input
          type="file"
          id="imageUpload"
          accept="image/*"
          onChange={handleFileChange}
          className="hidden"
        />
      </form>

      {/* Video URL Modal */}
      <VideoUrlModal
        isOpen={showVideoModal}
        onClose={() => setShowVideoModal(false)}
        onConfirm={handleVideoUrlConfirm}
        initialUrl={videoUrl}
      />
    </div>
  );
};

export default EditPostPage;
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';
import { uploadImage } from '../services/firebaseService';
import VideoUrlModal from '../components/VideoUrlModal';
import VideoIcon from '../components/icons/VideoIcon';
import ImageUploadIcon from '../components/icons/ImageUploadIcon';
import { getVideoInfo } from '../utils/videoUtils';

const CreatePostPage: React.FC = () => {
  const { currentUser } = useAuth();
  const { addPost } = usePosts();
  const navigate = useNavigate();

  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [videoUrl, setVideoUrl] = useState('');
  const [caption, setCaption] = useState('');
  const [contentBody, setContentBody] = useState('');
  const [tags, setTags] = useState(''); // Comma-separated
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);

  useEffect(() => {
    if (!currentUser) {
      navigate('/'); // Redirect if not logged in
    }
  }, [currentUser, navigate]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      setVideoUrl(''); // Clear video when image is selected
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setImageFile(null);
      setImagePreview(null);
    }
  };

  const handleVideoUrlConfirm = (url: string) => {
    setVideoUrl(url);
    setImageFile(null); // Clear image when video is selected
    setImagePreview(null);
  };

  const handleImageUploadClick = () => {
    const fileInput = document.getElementById('imageUpload') as HTMLInputElement;
    fileInput?.click();
  };

  if (!currentUser) {
    return (
      <div className="max-w-md mx-auto mt-10 p-6 bg-neutral-surface rounded-lg shadow-xl border border-neutral-border text-center">
        <p className="text-lg text-neutral-100">You must be logged in to create a post.</p>
        <button onClick={() => navigate('/')} className="mt-4 bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2 px-4 rounded-md transition-colors">
          Go Home
        </button>
      </div>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate that we have either media OR text content
    if (!imageFile && !videoUrl.trim() && !caption.trim() && !contentBody.trim()) {
      alert("Please add either media (image/video) or text content (caption/body) to your post.");
      return;
    }

    setIsUploading(true);
    try {
      let finalImageUrl = '';

      // If uploading a file, upload to Firebase Storage first
      if (imageFile) {
        const timestamp = Date.now();
        const fileName = `posts/${currentUser.id}/${timestamp}_${imageFile.name}`;
        finalImageUrl = await uploadImage(imageFile, fileName);
      } else if (videoUrl) {
        // For video posts, use the video thumbnail as the image
        const videoInfo = getVideoInfo(videoUrl);
        if (videoInfo) {
          finalImageUrl = videoInfo.thumbnailUrl;
        }
      }

      const postData: any = {
        userId: isAnonymous ? 'anonymous' : currentUser.id,
        username: isAnonymous ? 'Anonymous' : currentUser.username,
        userAvatarUrl: isAnonymous ? 'https://picsum.photos/seed/anonymous/100/100' : currentUser.avatarUrl,
        imageUrl: finalImageUrl,
        videoUrl: videoUrl || undefined,
        caption,
        contentBody,
        tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
        isAnonymous,
      };

      // Only add actualUserId if the post is anonymous
      if (isAnonymous) {
        postData.actualUserId = currentUser.id;
      }

      await addPost(postData);
      navigate('/');
    } catch (error) {
      console.error('Error creating post:', error);
      alert('Failed to create post. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="max-w-xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <h1 className="text-3xl font-bold text-brand-primary mb-4 text-center animate-text-glow hover-text-glow">Create New Post</h1>
      <p className="text-center text-neutral-300 mb-8 text-sm">
        Share your thoughts with <span className="text-brand-primary font-medium">text-only posts</span> or add media for enhanced content
      </p>
      <form onSubmit={handleSubmit} className="space-y-6 bg-neutral-surface p-6 sm:p-8 rounded-lg shadow-2xl border border-neutral-border animate-slide-up hover-glow cyber-border">

        {/* Media Preview Section */}
        {(imagePreview || videoUrl) && (
          <div>
            <label className="block text-sm font-medium text-neutral-300 mb-2">Media Preview</label>
            {imagePreview && (
              <div className="relative">
                <img src={imagePreview} alt="Preview" className="max-h-48 w-auto rounded-md border border-neutral-border" />
                <button
                  type="button"
                  onClick={() => {
                    setImageFile(null);
                    setImagePreview(null);
                  }}
                  className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 transition-colors duration-200"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}
            {videoUrl && (
              <div className="relative">
                <div className="bg-neutral-base border border-neutral-border rounded-md p-4">
                  <div className="flex items-center space-x-3">
                    <VideoIcon className="w-8 h-8 text-brand-primary" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-neutral-200">Video Link Added</p>
                      <p className="text-xs text-neutral-muted truncate">{videoUrl}</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => setVideoUrl('')}
                      className="bg-red-600 hover:bg-red-700 text-white rounded-full p-1 transition-colors duration-200"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        <div>
          <label htmlFor="caption" className="block text-sm font-medium text-neutral-300 mb-1">
            Caption (Short Summary) <span className="text-neutral-muted text-xs">- Optional for media posts</span>
          </label>
          <input
            id="caption"
            value={caption}
            onChange={(e) => setCaption(e.target.value)}
            maxLength={150}
            placeholder="A brief highlight or title for your post..."
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        <div>
          <label htmlFor="contentBody" className="block text-sm font-medium text-neutral-300 mb-1">
            Post Body <span className="text-neutral-muted text-xs">- Required for text-only posts</span>
          </label>
          <textarea
            id="contentBody"
            value={contentBody}
            onChange={(e) => setContentBody(e.target.value)}
            rows={6}
            placeholder="Share your detailed truth, insights, or story... (For text-only posts, this will be displayed prominently)"
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        <div>
          <label htmlFor="tags" className="block text-sm font-medium text-neutral-300 mb-1">
            Tags (comma-separated)
          </label>
          <input
            type="text"
            id="tags"
            value={tags}
            onChange={(e) => setTags(e.target.value)}
            placeholder="e.g., Truth, Sovereign, Decentralize"
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isAnonymous"
              checked={isAnonymous}
              onChange={(e) => setIsAnonymous(e.target.checked)}
              className="h-4 w-4 text-brand-primary bg-neutral-base border-neutral-border rounded focus:ring-brand-primary focus:ring-2"
            />
            <label htmlFor="isAnonymous" className="text-sm font-medium text-neutral-300">
              Post Anonymously
            </label>
          </div>
          <p className="text-xs text-neutral-muted">
            {isAnonymous
              ? "Your identity will be hidden from other users. Only the admin can see who posted this."
              : "Your post will be attributed to your username."
            }
          </p>

          {/* Bottom Action Bar */}
          <div className="flex items-center space-x-3">
            {/* Media Icons */}
            <div className="flex items-center space-x-2">
              <span className="text-xs text-neutral-muted">Optional media:</span>
              <button
                type="button"
                onClick={handleImageUploadClick}
                className="flex items-center justify-center w-10 h-10 bg-neutral-base hover:bg-brand-primary/20 border border-neutral-border hover:border-brand-primary text-neutral-300 hover:text-brand-primary rounded-md transition-all duration-200 hover-scale"
                title="Upload Image"
              >
                <ImageUploadIcon className="w-5 h-5" />
              </button>
              <button
                type="button"
                onClick={() => setShowVideoModal(true)}
                className="flex items-center justify-center w-10 h-10 bg-neutral-base hover:bg-brand-primary/20 border border-neutral-border hover:border-brand-primary text-neutral-300 hover:text-brand-primary rounded-md transition-all duration-200 hover-scale"
                title="Add Video Link"
              >
                <VideoIcon className="w-5 h-5" />
              </button>
            </div>

            {/* Post Button */}
            <button
              type="submit"
              disabled={isUploading}
              className="flex-1 bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2.5 px-4 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-surface focus:ring-brand-secondary transform hover:scale-105 hover:shadow-lg hover:shadow-brand-primary/25 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isUploading ? 'Creating Post...' : `Post as ${isAnonymous ? 'Anonymous' : currentUser.username}`}
            </button>
          </div>
        </div>

        {/* Hidden File Input */}
        <input
          type="file"
          id="imageUpload"
          accept="image/*"
          onChange={handleFileChange}
          className="hidden"
        />
      </form>

      {/* Video URL Modal */}
      <VideoUrlModal
        isOpen={showVideoModal}
        onClose={() => setShowVideoModal(false)}
        onConfirm={handleVideoUrlConfirm}
        initialUrl={videoUrl}
      />
    </div>
  );
};

export default CreatePostPage;
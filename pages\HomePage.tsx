
import React, { useState } from 'react';
import PostCard from '../components/PostCard';
import { usePosts } from '../hooks/usePosts';
import { useAuth } from '../hooks/useAuth';

const HomePage: React.FC = () => {
  const { posts, loading } = usePosts();
  const { loading: authLoading } = useAuth();
  const [showBanner, setShowBanner] = useState(true);

  if (authLoading || loading) {
    return (
      <div className="max-w-4xl mx-auto py-4 px-2 sm:px-4 lg:px-6">
        <div className="text-center text-neutral-muted py-10 animate-slide-up">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto mb-4"></div>
          <p className="text-xl animate-text-glow">Loading posts...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-4 px-2 sm:px-4 lg:px-6">
      {/* Beta Launch Banner */}
      {showBanner && (
        <div className="mb-6 bg-gradient-to-r from-brand-primary/20 via-brand-purple/20 to-brand-primary/20 border border-brand-primary/50 rounded-lg p-6 relative overflow-hidden animate-slide-up hover-glow cyber-border">
          {/* Animated background effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-brand-primary/10 to-transparent animate-pulse"></div>

          <div className="relative z-10 text-center">
            <div className="flex items-center justify-center mb-3">
              <div className="w-8 h-8 bg-brand-primary rounded-full flex items-center justify-center mr-3 animate-pulse-glow">
                <span className="text-neutral-base font-bold text-lg">🚀</span>
              </div>
              <h2 className="text-2xl font-bold text-brand-primary animate-text-glow">
                Welcome to the S3KT0R Beta!
              </h2>
            </div>

            <p className="text-neutral-200 text-lg mb-4 hover-text-glow transition-all duration-300">
              Thank you for being part of this <span className="text-brand-primary font-semibold">amazing beta launch</span>!
              Your participation helps us build the future of decentralized communication.
            </p>

            <p className="text-neutral-muted text-sm">
              Stay tuned as we roll out all the revolutionary features...
            </p>
          </div>

          {/* Dismiss button */}
          <button
            onClick={() => setShowBanner(false)}
            className="absolute top-3 right-3 text-neutral-muted hover:text-brand-primary transition-colors duration-200 hover-scale"
            aria-label="Dismiss banner"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      )}

      {/* Latest Features Section */}
      <div className="mb-6 bg-neutral-surface border border-neutral-border rounded-lg p-6 shadow-xl animate-slide-up hover-glow cyber-border">
        <div className="flex items-center mb-4">
          <div className="w-6 h-6 bg-gradient-to-r from-brand-primary to-brand-purple rounded-full flex items-center justify-center mr-3 animate-pulse-glow">
            <span className="text-white font-bold text-sm">✨</span>
          </div>
          <h3 className="text-xl font-bold text-brand-primary animate-text-glow">
            Latest Features
          </h3>
        </div>

        <div className="bg-neutral-base/50 border border-brand-primary/30 rounded-lg p-4 hover-glow transition-all duration-300">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-brand-purple/20 border border-brand-purple rounded-lg flex items-center justify-center mr-3">
              <svg className="w-5 h-5 text-brand-purple" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
              </svg>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-neutral-100 hover-text-glow transition-all duration-200">
                Video Embeds for your POSTS!
              </h4>
              <p className="text-neutral-muted text-sm">
                Share videos directly in your posts with seamless embedding
              </p>
            </div>
          </div>
        </div>
      </div>

      {posts.length === 0 && !loading && (
        <div className="text-center text-neutral-muted py-10 animate-slide-up">
          <p className="text-xl animate-text-glow">No posts yet.</p>
          <p className="hover-text-glow transition-all duration-300">Be the first to share something revolutionary!</p>
        </div>
      )}

      {/* Compact posts grid for better space utilization */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 lg:gap-4">
        {posts.map((post, index) => (
          <div key={post.id} style={{ animationDelay: `${index * 0.05}s` }}>
            <PostCard post={post} isCompact={true} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default HomePage;

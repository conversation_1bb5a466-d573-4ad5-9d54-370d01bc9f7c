<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Initialize Firestore Collections</title>
</head>
<body>
    <h1>Initialize Firestore Collections for S3Kt0R-Gram</h1>
    <button id="initCollections">Initialize Collections</button>
    <div id="result"></div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, doc, setDoc, collection } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        const firebaseConfig = {
            apiKey: "AIzaSyBz12g2Ilty-HnKNCLq4rD9q0bMivjcGUM",
            authDomain: "s3kt0r-30ede.firebaseapp.com",
            projectId: "s3kt0r-30ede",
            storageBucket: "s3kt0r-30ede.firebasestorage.app",
            messagingSenderId: "246065925479",
            appId: "1:246065925479:web:4c7a0f2660fcc9880af3c2",
            measurementId: "G-1R3KWWKXBM"
        };

        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        document.getElementById('initCollections').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Initializing collections...';

            try {
                // Initialize login screen config
                await setDoc(doc(db, 'config', 'loginScreen'), {
                    message: "Welcome to S3KT0R. The truth is in the code. Choose your path.",
                    imageUrl: "https://images.unsplash.com/photo-1505682499293-230dd9df9655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
                    imageOverlayOpacity: 0.5
                });

                // Create a placeholder document for posts collection (will be removed when first real post is created)
                await setDoc(doc(db, 'posts', '_placeholder'), {
                    _placeholder: true,
                    createdAt: new Date().toISOString()
                });

                // Create a placeholder document for adminMessages collection
                await setDoc(doc(db, 'adminMessages', '_placeholder'), {
                    _placeholder: true,
                    createdAt: new Date().toISOString()
                });

                resultDiv.innerHTML = `
                    <h2>Collections initialized successfully!</h2>
                    <ul>
                        <li>✅ config/loginScreen - Login screen configuration</li>
                        <li>✅ posts - Posts collection (with placeholder)</li>
                        <li>✅ adminMessages - Admin messages collection (with placeholder)</li>
                        <li>✅ users - Already exists with your admin account</li>
                    </ul>
                    <p>All collections are now ready for use!</p>
                    <p><strong>Next:</strong> You can now test your application with Firebase!</p>
                `;

            } catch (error) {
                console.error('Error initializing collections:', error);
                resultDiv.innerHTML = `
                    <p style="color: red;">Error: ${error.message}</p>
                    <p style="color: orange;">If you see permission errors, make sure you're logged in as an admin user in Firebase.</p>
                    <p style="color: blue;">The Chrome extension errors are unrelated and can be ignored.</p>
                `;
            }
        });
    </script>
</body>
</html>

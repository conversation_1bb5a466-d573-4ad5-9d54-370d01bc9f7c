
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

const SignupPage: React.FC = () => {
  const { signUpUser } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [username, setUsername] = useState('');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setMessage('');
    setIsLoading(true);

    // Validation
    if (!email.trim() || !password.trim() || !username.trim()) {
      setError("All fields are required.");
      setIsLoading(false);
      return;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      setIsLoading(false);
      return;
    }

    if (password.length < 6) {
      setError("Password must be at least 6 characters long.");
      setIsLoading(false);
      return;
    }

    try {
      const newUser = await signUpUser(email.trim(), password, username.trim());
      if (newUser) {
        setMessage(`Welcome to S3KT0R, ${newUser.username}! Your account has been created successfully.`);
        setEmail('');
        setPassword('');
        setConfirmPassword('');
        setUsername('');

        // Navigate to home page after a delay to show success message
        setTimeout(() => {
          navigate('/');
        }, 2000);
      }
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred during sign up.");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <h1 className="text-3xl font-bold text-brand-primary mb-8 text-center animate-text-glow hover-text-glow">Sign Up for S3KT0R</h1>
      {message && <p className="mb-4 text-center text-accent-success bg-green-500/10 p-3 rounded-md animate-scale-in hover-glow">{message}</p>}
      {error && <p className="mb-4 text-center text-accent-error bg-red-500/10 p-3 rounded-md animate-scale-in animate-cyber-flicker">{error}</p>}
      <form onSubmit={handleSubmit} className="space-y-6 bg-neutral-surface p-6 sm:p-8 rounded-lg shadow-2xl border border-neutral-border animate-slide-up hover-glow cyber-border">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-neutral-300 mb-1">
            Email
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email address"
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200 hover:border-brand-primary/50 hover-glow"
            required
          />
        </div>

        <div>
          <label htmlFor="username" className="block text-sm font-medium text-neutral-300 mb-1">
            Username
          </label>
          <input
            type="text"
            id="username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="Choose your anonymous handle"
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200 hover:border-brand-primary/50 hover-glow"
            required
          />
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-neutral-300 mb-1">
            Password
          </label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter your password"
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200 hover:border-brand-primary/50 hover-glow"
            required
          />
        </div>

        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-neutral-300 mb-1">
            Confirm Password
          </label>
          <input
            type="password"
            id="confirmPassword"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Confirm your password"
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200 hover:border-brand-primary/50 hover-glow"
            required
          />
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-brand-primary hover:bg-brand-secondary disabled:bg-neutral-muted disabled:cursor-not-allowed text-white font-semibold py-2.5 px-4 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-surface focus:ring-brand-secondary transform hover:scale-105 hover:shadow-lg hover:shadow-brand-primary/25 hover-glow disabled:transform-none disabled:shadow-none"
        >
          {isLoading ? 'Creating Account...' : 'Sign Up'}
        </button>
      </form>
       <p className="mt-6 text-center text-sm text-neutral-muted">
        Already have an account or been approved?{' '}
        <button onClick={() => navigate('/')} className="font-medium text-brand-primary hover:text-brand-secondary transition-all duration-200 hover-text-glow hover-scale">
          Login from Navbar
        </button>
      </p>
    </div>
  );
};

export default SignupPage;
